package com.example.pure.config;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.filter.JwtFilter;
import com.example.pure.handler.CustomAccessDeniedHandler;
import com.example.pure.handler.CustomAuthenticationEntryPoint;
import com.example.pure.security.CustomUserDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security 配置类
 *
 * SpringBoot运行程序时会自动扫描@Configuration注解的类，并自动配置Spring Security，先把配置类下的@Bean注解的方法计算出
 * 结果,然后返回结果到Bean里,存放到Bean里的类型和名称和方法是一样的自动配置的spring-boot-autoconfigure.jar包里，有一个
 * 负责配置Security的类，里面会有一个和@Bean同样类型的对象Springboot会自动注入到和@Bean类型一样的对象里(名称是库开发者
 * 定义的可能和我们的Bean名称不一样)，依靠类型类注入
 *
 *
 * <p>配置Spring Security的核心功能，包括：
 * <ul>
 *     <li>认证管理器配置</li>
 *     <li>安全过滤链配置</li>
 *     <li>密码编码器配置</li>
 *     <li>跨域和CSRF配置</li>
 *     <li>会话管理配置</li>
 *     <li>异常处理配置</li>
 * </ul>
 * </p>
 *
 * <p>主要特性：
 * <ul>
 *     <li>基于JWT的无状态认证</li>
 *     <li>自定义认证和授权处理</li>
 *     <li>细粒度的接口权限控制</li>
 *     <li>支持方法级别的安全注解</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)//启用方法级别的控制，控制器上使用注解来进行权限控制
public class SecurityConfig {

    private final CustomUserDetailsService userDetailsService;
    private final JwtFilter jwtFilter;
    private final CustomAuthenticationEntryPoint authenticationEntryPoint;
    private final CustomAccessDeniedHandler accessDeniedHandler;

    @Autowired
    public SecurityConfig(
            CustomUserDetailsService userDetailsService,
            JwtFilter jwtFilter,
            CustomAuthenticationEntryPoint authenticationEntryPoint,
            CustomAccessDeniedHandler accessDeniedHandler) {
        // 从spring的Bean自动注入相当于this.userDetailsService=new CustomUserDetailsService();
        this.userDetailsService = userDetailsService;
        this.jwtFilter = jwtFilter;
        this.authenticationEntryPoint = authenticationEntryPoint;
        this.accessDeniedHandler = accessDeniedHandler;
    }

    /*
     * AuthenticationManager Bean 定义（已移除）
     *
     * 在现代 Spring Security（尤其是 Spring Boot 2.7+）中，
     * 显式定义 AuthenticationManager bean 通常是不必要的。
     *
     * Spring Boot 的自动配置将根据上下文中找到的 UserDetailsService
     * 和 PasswordEncoder bean 自动配置 AuthenticationManager。
     *
     * 如果需要在其他地方注入 AuthenticationManager（例如在 AuthService 中），
     * 请注入 AuthenticationConfiguration 并使用
     * authenticationConfiguration.getAuthenticationManager() 获取认证管理器。
     */
    // @Bean // 不再需要此 Bean
    // public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
    //    AuthenticationManagerBuilder authenticationManagerBuilder =
    //            http.getSharedObject(AuthenticationManagerBuilder.class);
    //    authenticationManagerBuilder
    //            .userDetailsService(userDetailsService)
    //            .passwordEncoder(passwordEncoder());
    //    return authenticationManagerBuilder.build();
    // }

    /**
     * 配置安全过滤链
     * 定义系统的安全策略、规则和各种过滤器
     *
     * <p>主要配置：
     * <ul>
     *     <li>允许跨域请求</li>
     *     <li>禁用CSRF（使用JWT时不需要）</li>
     *     <li>配置无状态会话</li>
     *     <li>配置异常处理</li>
     *     <li>配置路径访问权限</li>
     *     <li>添加JWT过滤器</li>
     * </ul>
     * </p>
     *
     * @param http HttpSecurity配置对象
     * @return SecurityFilterChain 安全过滤链
     * @throws Exception 配置异常
     */
    @Bean // Bean方法级别的注解,把方法的代码计算完，以类型加方法名为Bean返回结果存放到Bean,用时在那个类使用@Autowired注入到类
    public SecurityFilterChain filterChain(@NonNull HttpSecurity http) throws Exception {
        http    //1.全局安全配置
                //链式方法返回对象是因为http.cors返回后就等于http.and（），直接返回对象的话加.方法就能运行下一个方法
                .cors()
                .and()
                .csrf().disable()
                .formLogin().disable()    // 全局禁用表单登录,不需要Session
                .httpBasic().disable()    // 全局禁用基础认证,使用Token，不需要每次都在请求输入密码
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()

                // 2. 异常处理,请求在进入身份验证进入控制器前(安全过滤链),出现的异常无法被通用异常处理器的处理器捕获，只
                //    只能添加自定义独立的处理器来处理异常
                .exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint)//认证失败
                .accessDeniedHandler(accessDeniedHandler)//访问拒绝
                .and()

                // 3. 权限配置，运行过滤器是通过下面的antMatchers（）代码，只有用户接口会运行过滤器
                .authorizeRequests()
                // 3.1 公开接口
                .antMatchers(SecurityConstants.PUBLIC_URLS).permitAll()
                .antMatchers(SecurityConstants.SWAGGER_URLS).permitAll()
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()

                //HTTP请求级别验证，从SecurityContextHolder获取当前角色，用来检查当前角色信息
                // 3.2 用户接口 - 需要USER或ADMIN角色，Spring Security验证权限时自动添加ROLE_前缀给USER或ADMIN
                .antMatchers(SecurityConstants.USER_URLS)
                .hasAnyRole("USER", "ADMIN","COMMON")

                // 3.3 管理接口 - 只需要ADMIN角色
                .antMatchers(SecurityConstants.ADMIN_URLS)
                .hasRole("ADMIN")

                // 3.4 其他请求需要认证
                .anyRequest().authenticated()
                .and()
                //方法第二个参数用.class因为要让SpringSecurity知道类的位置
                // 4. 配置添加现在这个过滤器前的先运行哪些过滤器，如果代码关联2个过滤器，设置过滤器运行顺序，先运行Jwt过滤器，在运行密码验证过滤器
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);

        // 5. 添加缓存控制
        http.headers().cacheControl();

        return http.build();
        /* Lambda写法
         * http.sessionManagement(session ->
            session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
            http.exceptionHandling(exception ->
            exception.authenticationEntryPoint(authenticationEntryPoint)
            .accessDeniedHandler(accessDeniedHandler));
            http.authorizeRequests(authorize ->
            authorize.antMatchers(HttpMethod.POST, "/api/auth/login").permitAll()

         */
    }

    /**
     * 配置密码编码器
     * 使用BCrypt强哈希算法
     *
     * @return PasswordEncoder BCrypt密码编码器实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
